const Joi = require('joi');

/* callout validation */
module.exports = {
  /* Id validation */
  idSchema: Joi.object({
    id: Joi.string().required(),
    buttonLink: Joi.string().allow(null),
  }),

  params: Joi.object({
    id: Joi.string().required(),
  }),
  /* Email validation */
  emailSchema: Joi.object({
    email: Joi.string().required().trim(),
  }),

  createSchema: Joi.object({
    name: Joi.string().required(),
    body: Joi.object({
      companyName: Joi.string(),
      companyProfile: Joi.string().allow(null),
      content: Joi.string().allow(null),
      logo: Joi.string(),
      title: Joi.string(),
      companyProfileHeading: Joi.string(),
    }),
    genres: Joi.string(),
    prompt: Joi.string(),
    opportunities: Joi.string(),
    budget: Joi.number(),
    city: Joi.object({
      address: Joi.string().required(),
      geoCode: Joi.object(),
    }),
    discovererId: Joi.string(),
    requiredFields: Joi.object({
      cover: Joi.object({
        allFields: Joi.boolean(),
        fields: Joi.object({
          coverImage: Joi.boolean(),
          title: Joi.boolean(),
          producer: Joi.boolean(),
          director: Joi.boolean(),
          writer: Joi.boolean(),
        }),
      }),
      basicInformation: Joi.object({
        allFields: Joi.boolean(),
        fields: Joi.object({
          logline: Joi.boolean(),
          tags: Joi.boolean(),
          genre: Joi.boolean(),
          setting: Joi.boolean(),
          runtime: Joi.boolean(),
          status: Joi.boolean(),
          projectHighlights: Joi.boolean(),
        }),
      }),
      description: Joi.object({
        allFields: Joi.boolean(),
        fields: Joi.object({
          synopsis: Joi.boolean(),
          creativeVision: Joi.boolean(),
          treatmentBible: Joi.boolean(),
          script: Joi.boolean(),
        }),
      }),
      creativeTeam: Joi.boolean(),
      castMembers: Joi.boolean(),
      moodboard: Joi.boolean(),
      projectVideos: Joi.boolean(),
      financePlan: Joi.boolean(),
      budget: Joi.boolean(),
      otherDocuments: Joi.boolean(),
      salesEstimate: Joi.boolean(),
    }),
  }),

  /* Delete agent schema validation */
  deleteSchema: Joi.object({
    id: Joi.array().required(),
  }),

  updateSchema: Joi.object({
    name: Joi.string(),
    body: Joi.object({
      companyName: Joi.string(),
      companyProfile: Joi.string().allow(null),
      content: Joi.string().allow(null),
      logo: Joi.string(),
      title: Joi.string(),
      companyProfileHeading: Joi.string(),
    }),
    genres: Joi.string(),
    prompt: Joi.string(),
    opportunities: Joi.string(),
    budget: Joi.number(),
    city: Joi.object({
      address: Joi.string().required(),
      geoCode: Joi.object(),
    }),
    discovererId: Joi.string(),
    deleted: Joi.bool().optional().valid(false, true),
    isPublished: Joi.bool(),
    status: Joi.string(),
    requiredFields: Joi.object({
      cover: Joi.object({
        allFields: Joi.boolean().optional(),
        fields: Joi.object({
          coverImage: Joi.boolean().optional(),
          title: Joi.boolean().optional(),
          producer: Joi.boolean().optional(),
          director: Joi.boolean().optional(),
          writer: Joi.boolean().optional(),
        }).optional(),
      }).optional(),
      basicInformation: Joi.object({
        allFields: Joi.boolean().optional(),
        fields: Joi.object({
          logline: Joi.boolean().optional(),
          tags: Joi.boolean().optional(),
          genre: Joi.boolean().optional(),
          setting: Joi.boolean().optional(),
          runtime: Joi.boolean().optional(),
          status: Joi.boolean().optional(),
          projectHighlights: Joi.boolean().optional(),
        }).optional(),
      }).optional(),
      description: Joi.object({
        allFields: Joi.boolean().optional(),
        fields: Joi.object({
          synopsis: Joi.boolean().optional(),
          creativeVision: Joi.boolean().optional(),
          treatmentBible: Joi.boolean().optional(),
          script: Joi.boolean().optional(),
        }).optional(),
      }).optional(),
      creativeTeam: Joi.boolean().optional(),
      castMembers: Joi.boolean().optional(),
      moodboard: Joi.boolean().optional(),
      projectVideos: Joi.boolean().optional(),
      financePlan: Joi.boolean().optional(),
      budget: Joi.boolean().optional(),
      otherDocuments: Joi.boolean().optional(),
      salesEstimate: Joi.boolean().optional(),
    }).optional(),
  }),

  submissionSchema: Joi.object({
    id: Joi.string().required(),
    body: Joi.string().required(),
  }),

  slateSchema: Joi.object({
    id: Joi.string().required(),
    notes: Joi.string().allow(null, ''),
  }),

  submissionAddSchema: Joi.object({
    id: Joi.string().required(),
  }),

  slateStatusSchema: Joi.object({
    id: Joi.string().required(),
    status: Joi.string()
      .required()
      .valid('awaiting_feedback', 'lets_talk', 'tracking', 'not_interested'),
  }),

  submissionStatusSchema: Joi.object({
    id: Joi.string().required(),
    status: Joi.string().required().valid('NEW', 'REJECTED', 'FEEDBACK_SENT'),
  }),

  submissionFeedbackSchema: Joi.object({
    id: Joi.string().required(),
    feedback: Joi.string().required(),
  }),
};
